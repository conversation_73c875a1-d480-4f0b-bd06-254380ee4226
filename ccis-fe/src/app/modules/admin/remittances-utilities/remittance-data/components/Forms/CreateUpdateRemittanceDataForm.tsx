// Important imports
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import TextField from "@components/form/TextField";
// * We declare the useFormik for insertion and some neat trick for the combo box
import { Form, FormikProvider, useFormik } from "formik";
// * We use the useMemo for listing off the data from RemittanceType
import React, { useEffect, useMemo } from "react";

// * Selector for Redux hooks to access state
import { useSelector } from "react-redux";
// * For use in above selector
import { RootState } from "@state/store";

// * Declaring IRemittanceType for the data type
import { IRemittanceType, IUtilitiesRemittanceData } from "@interface/utilities.interface";

// * For the Select Component
import Select from "@modules/sales/components/select";

// Schema to be used for both create and edit
import { CreateRemittanceDataSchema, EditRemittanceDataSchema } from "@services/utilities-remittance-data/utilities-remittance-data.schema";
// Toast for the in-page notifications
import { toast } from "react-toastify";
//To load in the function to getRemittanceType
import { useRemittanceTypeManagementActions } from "@state/reducer/utilities-remittance-type";

// Setting up the Modal Props for the Remittance Data
type RemittanceDataModalProps = {
  isOpen: boolean;
  isEditMode: boolean;
  initialValues: IUtilitiesRemittanceData;
  onClose: () => void;
  onSubmits: (values: IUtilitiesRemittanceData) => void;
};

// All of the properties that we would use for this Modal prop
const CreateUpdateRemittanceDataForm: React.FC<RemittanceDataModalProps> = ({ isOpen, isEditMode, initialValues, onClose, onSubmits }) => {
  //To load for the Remittance Type
  const { getRemittanceType } = useRemittanceTypeManagementActions();
  //For the payload shell of the Remittance Type
  const remittanceTypeData = useSelector((state: RootState) => state?.utilitiesRemittanceTypes?.remittanceTypes);

  //For Data Validation
  const formik = useFormik({
    initialValues,
    enableReinitialize: true, // <-- Ensure form is reset when initialValues change
    validationSchema: isEditMode ? EditRemittanceDataSchema : CreateRemittanceDataSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        await onSubmits(values);
        resetForm();
        onClose();
      } catch (error) {
        toast.error(`Error submitting form: ${(error as any).message}`);
      }
    },
  });

  // * To setup the options for the Remittance Type
  // * Actual Data! - Remittance Type
  const remittanceTypeSelectItems = useMemo(
    () =>
      remittanceTypeData
        //To sort the map in ascending Remittance Type ID order
        .slice()
        .sort((a: IRemittanceType, b: IRemittanceType) => Number(a.id) - Number(b.id))
        .map((remittanceType: IRemittanceType) => ({
          text: remittanceType.remittanceTypeName,
          value: remittanceType.id?.toString() ?? "",
        })),
    [remittanceTypeData]
  );

  // * So that we can assign a Remittance Type ID every time we change the Remittance Type Name in the combo box:
  const handleSelectChange = (value: string) => {
    formik.setFieldValue("remittanceTypeId", parseInt(value));
  };

  //Set the initial values here:
  useEffect(() => {
    getRemittanceType({params: { filter: "" }});
    if (isEditMode) {
      formik.setValues(initialValues);
      formik.setFieldValue("remittanceTypeId", initialValues.remittanceTypeId);
    }
  }, [initialValues, isEditMode]);

  return (
    <Modal title={isEditMode ? "Edit Remittance Data" : "Create New Remittance Data"} modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={isOpen} onClose={onClose}>
      <FormikProvider value={formik}>
        <Form className="min-w-full my-4">
          <div className="flex-none w-full my-4">
            <label>Period From</label>
            <TextField
              name="periodFrom"
              placeholder="Enter Period From"
              type="date"
              className="bg-white"
              error={formik.touched.periodFrom && !!formik.errors.periodFrom}
              errorText={formik.errors.periodFrom}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.periodFrom}
              required
            />
          </div>

          <div className="flex-none w-full my-4">
            <label>Period To</label>
            <TextField
              name="periodTo"
              placeholder="Enter Period To"
              type="date"
              className="bg-white"
              error={formik.touched.periodTo && !!formik.errors.periodTo}
              errorText={formik.errors.periodTo}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.periodTo}
              required
            />
          </div>

          <div className="flex-none w-full my-4">
            <label>Remittance Type</label>
            <Select
              name="remittanceTypeId"
              placeholder="Select Remittance Type"
              options={remittanceTypeSelectItems}
              onChange={handleSelectChange}
              value={formik?.values?.remittanceTypeId?.toString() || ""} // Bind to the correct formik state
              error={formik.touched.remittanceTypeId && !!formik.errors.remittanceTypeId}
              errorText={formik.errors.remittanceTypeId}
              onBlur={formik.handleBlur}
              required
            />
          </div>
          <div className="flex w-full mt-6">
            <Button type="submit" variant="primary" classNames="w-full justify-center btn rounded-xl">
              {isEditMode ? "Save Changes" : "Submit"}
            </Button>
          </div>
        </Form>
      </FormikProvider>
    </Modal>
  );
};

export default CreateUpdateRemittanceDataForm;
