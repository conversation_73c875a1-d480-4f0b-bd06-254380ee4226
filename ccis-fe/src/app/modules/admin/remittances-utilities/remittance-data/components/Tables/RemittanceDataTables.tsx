// For specific React components
import React, { Fragment, useEffect, useState } from "react";
// Selector for Redux hooks to access state
import { useSelector } from "react-redux";
// Table Components, specifically the column
import { TableColumn } from "react-data-table-component";
// Other GUI elements and functions
import ActionButtons from "@components/common/ActionButtons";
import Table from "@components/common/Table";
import { confirmDelete, confirmSaveOrEdit } from "@helpers/prompt";
// Interface Actions
import { IActions } from "@interface/common.interface";

// Interface for the Remittance Data
import { IUtilitiesRemittanceData } from "@interface/utilities.interface";
// Reducer for connecting the service to the Saga
import { useRemittanceDataManagementActions } from "@state/reducer/utilities-remittance-data";

// Other misc. imports
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";

// Import the form that would be used for the table
import CreateUpdateRemittanceDataForm from "../Forms/CreateUpdateRemittanceDataForm.tsx";

// Setting up the table
const RemittanceDataTable: React.FC = () => {
  //Setting up the settings for the table, as well as variables
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<IUtilitiesRemittanceData>({} as IUtilitiesRemittanceData);
  const [searchText, setSearchText] = useState<string>("");

  //For the Remittance type and the loading service
  //First line is important to check for the actual data for remittance datas.
  const remittanceDatas = useSelector((state: RootState) => state?.utilitiesRemittanceData?.remittanceDatas);

  //For the loading function
  const loading = useSelector((state: RootState) => state.utilitiesRemittanceData?.getRemittanceData?.loading);

  //For the actions that would be used for the table
  const { getRemittanceData, setSelectedRemittanceData, postRemittanceData, putRemittanceData, destroyRemittanceData } = useRemittanceDataManagementActions();
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  //For the Edit button
  const actionEvents: IActions<IUtilitiesRemittanceData>[] = [
    {
      name: "Edit",
      event: (row: IUtilitiesRemittanceData, index: number) => {
        const data = {
          id: row.id,
          periodFrom: row.periodFrom,
          periodTo: row.periodTo,
          remittanceTypeId: row.remittanceTypeId,
        };
        setSelectedRemittanceData({ data: data, index: index });
        setInitialValues(data);
        setIsEditMode(true);
        setModalOpen(true);
      },
      icon: CiEdit,
      color: "primary",
      disabled: false,
    },
    //For the Delete button
    {
      name: "Delete",
      event: (row: IUtilitiesRemittanceData, index: number) => {
        const action = confirmDelete(row.requirementTemplate?.requirementTemplateName || "this item");
        action.then((value) => {
          if (value.isConfirmed) {
            destroyRemittanceData({ id: row.id, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
      //Below are the codes for disabling deletes except for specific users
      //Specifically, the superadmin
      //disabled: true,
    },
  ];

  //For the columns that would be used for the table
  const columns: TableColumn<IUtilitiesRemittanceData>[] = [
    {
      name: "Period From",
      selector: (row) => row.periodFrom,
      ...commonSetting,
    },
    {
      name: "Period To",
      cell: (row) => row.periodTo,
    },
    {
      name: "Remittance Type Name",
      cell: (row) => row.remittanceType?.remittanceTypeName,
    },
    {
      name: "Description",
      cell: (row) => row.remittanceType?.description,
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];

  //This is for the create modal,
  // mainly to initialize the values
  const handleToggleModal = () => {
    setModalOpen((prev) => !prev);
    setIsEditMode(false);
    setInitialValues({
      id: 0,
      periodFrom: "",
      periodTo: "",
      remittanceTypeId: "",
    });
  };

  const handleSubmit = async (values: IUtilitiesRemittanceData) => {
    if (isEditMode) {
      const isConfirmed = await confirmSaveOrEdit(`Do you want to edit this Remittance Data?"`);
      if (isConfirmed) {
        putRemittanceData(values);
      }
    } else {
      const isConfirmed = await confirmSaveOrEdit(`Do you want to save this Remittance Data?"`);
      if (isConfirmed) {
        postRemittanceData(values);
      }
    }
  };

  //For filtering stuff when searching,
  // this would be attached to the search text
  useEffect(() => {
    getRemittanceData({
      params: {
        filter: searchText,
      },
    });
  }, [searchText]);

  // Actual HTML output
  return (
    <Fragment>
      <div className="text-xl font-semibold uppercase my-4">Remittance Data</div>
      <Table
        className="h-[400px] "
        columns={columns}
        data={remittanceDatas}
        createLabel="Create Remittance Data"
        onCreate={handleToggleModal}
        loading={loading}
        disabled={false}
        onSearch={setSearchText}
        multiSelect={false}
        searchable
      />
      <CreateUpdateRemittanceDataForm isOpen={modalOpen} isEditMode={isEditMode} initialValues={initialValues} onClose={handleToggleModal} onSubmits={handleSubmit} />
    </Fragment>
  );
};

// for exporting the table element to other modules
export default RemittanceDataTable;
