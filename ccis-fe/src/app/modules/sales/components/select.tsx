import { Fragment, useEffect, useRef, useState, MouseEvent, FocusEvent, useMemo } from "react";
import classNames from "classnames";
import ErrorText from "@components/common/ErrorText";
import { ISelectOptions } from "@interface/common.interface";
import colorMode from "@modules/sales/utility/color";
import Input from "./input";
import CheckBox from "@components/form/CheckBox";

export type TOptions = ISelectOptions & {
  value: string;
  text: string;
  disabled?: boolean;
};

type TSelectProps = {
  options?: TOptions[] | ISelectOptions[];
  value?: string | null;
  placeholder?: string;
  name?: string;
  id?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  error?: boolean;
  errorText?: string;
  errorIcon?: boolean;
  onChange?: (value: string) => void;
  onBlur?: (e: FocusEvent<HTMLDivElement>) => void;
  children?: React.ReactNode;
  allowSearch?: boolean;
  placeHolderCenter?: boolean;
  multi?: boolean;
  onSearch?: (searchTerm: string) => void;
};

export default function Select({
  options = [],
  value = "",
  placeholder = "Choose from options",
  required = false,
  className = "",
  disabled = false,
  error = false,
  errorText,
  errorIcon = false,
  onChange = () => {},
  children = null,
  allowSearch = false,
  placeHolderCenter = true,
  multi = false,
  onSearch = () => {},
}: TSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selected, setSelected] = useState<string>(value ?? "");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownMenuRef = useRef<HTMLDivElement>(null);

  const selectedOption = options.find((opt) => opt.value == selected);
  const isSelectPlaceholder = placeholder == "-- Select --" || placeholder == "--- Select ---";

  const dropdownClass = classNames(
    "relative w-full border !rounded-lg focus:!outline-2",
    {
      "border-error": error,
      "bg-gray-100": disabled,
      "cursor-not-allowed": disabled,
    },
    className,
    {
      "border border-gray/10": !error && !disabled,
    },
    colorMode({
      classLight: "border-gray/10 bg-white outline-2 outline-gray/10",
      classDark: "border-primary bg-black/10 outline-2 outline-primary",
    })
  );

  const buttonClass = classNames(
    "w-full px-4 py-2 text-left flex justify-between items-center text-nowrap !rounded-[inherit]",
    {
      "cursor-not-allowed": disabled,
      "bg-gray-100": disabled,
      "text-center justify-center": !selectedOption && isSelectPlaceholder,
    },
    colorMode({
      classLight: "bg-white outline-2 outline-gray/10",
      classDark: "bg-black/10 outline-2 outline-primary",
    })
  );

  const optionsClass = classNames(
    "absolute z-50 border rounded-lg shadow-lg border-gray/20 max-h-[300px] overflow-y-auto",
    {
      hidden: !isOpen,
    },
    colorMode({
      classLight: "bg-[#fafafa]",
      classDark: "bg-[#1a1a1a]",
    })
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && dropdownMenuRef.current && !dropdownMenuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen && dropdownRef.current && dropdownMenuRef.current) {
        const rect = dropdownRef.current.getBoundingClientRect();
        dropdownMenuRef.current.style.width = `${rect.width}px`;
        dropdownMenuRef.current.style.left = `${rect.left}px`;
        dropdownMenuRef.current.style.top = `${rect.bottom}px`;
      }
    };

    document.addEventListener("mousedown", handleClickOutside as any);
    window.addEventListener("scroll", handleScroll, true);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside as any);
      window.removeEventListener("scroll", handleScroll, true);
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && dropdownRef.current && dropdownMenuRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      dropdownMenuRef.current.style.width = `${rect.width}px`;
      dropdownMenuRef.current.style.left = `${rect.left}px`;
      dropdownMenuRef.current.style.top = `${rect.bottom}px`;
    }
  }, [isOpen]);

  // Update selected value when prop changes
  useEffect(() => {
    if (value) setSelected(value);
  }, [value]);

  useEffect(() => {
    if (value) onChange(value);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelect = (value: string) => {
    if (multi) return;
    setSelected(value);
    setIsOpen(false);
    onChange(value);
  };

  const [multiple, setMultiple] = useState<string[]>([]);

  const [search, setSearch] = useState("");

  const filteredOptions = useMemo(() => {
    return options.filter((opt) => {
      return opt.text.toLowerCase().includes(search.toLowerCase());
    });
  }, [search, options]);

  const displayPlaceholder = useMemo(() => {
    if (!multi) return selectedOption ? selectedOption.text : placeholder;

    if (multiple.length === 0) return placeholder;
    // Display in comma separated format
    return multiple
      .map((val) => {
        const opt = options.find((opt) => opt.value === val);
        return opt ? opt.text : "";
      })
      .join(", ");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [multi, filteredOptions, multiple, selectedOption, placeholder, value]);

  useEffect(() => {
    if (onSearch) {
      onSearch(search);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  return (
    <Fragment>
      <div ref={dropdownRef} className={dropdownClass}>
        <button type="button" onClick={() => !disabled && setIsOpen(!isOpen)} className={`${buttonClass} ${disabled ? "bg-zinc-300  cursor-not-allowed" : ""}`} disabled={disabled}>
          <span className={!selectedOption && isSelectPlaceholder ? "flex-grow text-center" : ""}>{displayPlaceholder}</span>
          {selectedOption && <span className="ml-2">▼</span>}
          {!selectedOption && <span className="absolute right-4">▼</span>}
        </button>
      </div>

      {isOpen && (
        <div ref={dropdownMenuRef} className={optionsClass} style={{ position: "fixed" }}>
          {allowSearch && (
            <div className="px-4 py-2">
              <Input placeholder="Search" className="border-none" value={search} onChange={(e) => setSearch(e.target.value)} />
            </div>
          )}

          {required ? null : (
            <div
              className={classNames("px-4 py-2 hover:bg-gray-100 cursor-pointer text-center", {
                "text-center": placeHolderCenter,
                "text-start": !placeHolderCenter,
              })}
              onClick={() => handleSelect("")}
            >
              {placeholder}
            </div>
          )}

          {filteredOptions.map((opt, index) => (
            <div
              key={`opt-${index}`}
              className={classNames("px-4 py-2 cursor-pointer", {
                "hover:bg-gray-100": !opt.disabled,
                "bg-zinc-300 cursor-not-allowed opacity-50": opt.disabled,
                "bg-primary/10": opt.value === selected,
              })}
              onClick={() => {
                !opt.disabled && handleSelect(opt.value);
              }}
            >
              <div className={`flex flex-row flex-nowrap gap-2 items-center `}>
                {multi && (
                  <CheckBox
                    checked={multiple.includes(opt.value)}
                    disabled={opt.disabled}
                    onChange={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      if (!multi) return;
                      if (e.target.checked) {
                        setMultiple((prev) => [...prev, opt.value]);
                        onChange(JSON.stringify([...multiple, opt.value]));
                      } else {
                        const newMultiple = multiple.filter((val) => val !== opt.value);
                        setMultiple(newMultiple);
                        onChange(JSON.stringify(newMultiple));
                      }
                    }}
                  />
                )}
                {opt.text ?? opt}
              </div>
            </div>
          ))}

          {children && (
            <div className="px-4 py-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleSelect("add-new")}>
              {children}
            </div>
          )}
        </div>
      )}

      {error && <ErrorText text={errorText} withIcon={errorIcon} />}
    </Fragment>
  );
}
